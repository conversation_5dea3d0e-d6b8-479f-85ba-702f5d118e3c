import { useNavigate } from "react-router-dom";
import { useState } from "react";
import Select from "react-select";
import OrderEditPopup from "../../components/Popups/OrderEditPopup";
import RoughEstimatePopup from "../../components/Popups/RoughEstimatePopup";
import "react-datepicker/dist/react-datepicker.css";
import { useTranslation } from "react-i18next";

// React-icons
import { FaSearch, FaPlus } from "react-icons/fa";

function ReportListPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [showPopup, setShowPopup] = useState(false);
  const [showCreateOrderPopup, setShowCreateOrderPopup] = useState(false);
  const [showRoughEstimatePopup, setShowRoughEstimatePopup] = useState(false);

  // Initialize with empty values for creating a new order
  const [orderFormData, setOrderFormData] = useState({
    orderDate: null,
    orderingCompany: "",
    deliveryDate: null,
    contactPerson: "",
    deliveryDestination: "",
    shippingClass: null,
    siteName: "",
  });

  // Material form data for the popup selections
  const [materialFormData, setMaterialFormData] = useState({
    material: "Aluminum",
    type: "TS-1N",
    thickness: "t-1.0",
    curvedObject: "Curved Object 1",
    bis: "BIS 1",
    breakingPoint: "Breaking Point 1",
    paint: "Paint type 1",
    color: "Color type 1",
  });

  // Mock project data
  const mockProjects = [
    {
      id: "00201565",
      company: "Okayama Building Sash Industry Co., Ltd.",
      date: "2025/01/06",
      projectName: "(ML) Yonago Nishinippon Co., Ltd. Factory",
    },
    {
      id: "00201506",
      company: "O-RISE Co., Ltd. Head Office",
      date: "2025/12/06",
      projectName: "Tobu Junior High School Renovation Project Building",
    },
    {
      id: "00201488",
      company: "Ace Plan",
      date: "2025/12/06",
      projectName: "FLEX Uji Okubocho Project",
    },
    {
      id: "00201484",
      company: "O- RISE Co., Ltd. Osaka Branch",
      date: "2025/12/06",
      projectName: "2025 Osaka Kansai World Expo TECH WORLD",
    },
  ];

  // Options for select components
  const orderOptions = [
    { value: "order1", label: "Order #12345" },
    { value: "order2", label: "Order #67890" },
    { value: "order3", label: "Order #54321" },
  ];

  const materialOptions = [
    { value: "Aluminum", label: "Aluminum" },
    { value: "Steel", label: "Steel" },
    { value: "Plastic", label: "Plastic" },
  ];

  const typeOptions = [
    { value: "TS-1N", label: "TS-1N" },
    { value: "TS-2N", label: "TS-2N" },
    { value: "TS-3N", label: "TS-3N" },
  ];

  const thicknessOptions = [
    { value: "t-1.0", label: "t-1.0" },
    { value: "t-1.5", label: "t-1.5" },
    { value: "t-2.0", label: "t-2.0" },
  ];

  const curvedObjectOptions = [
    { value: "Curved Object 1", label: "Curved Object 1" },
    { value: "Curved Object 2", label: "Curved Object 2" },
    { value: "Curved Object 3", label: "Curved Object 3" },
  ];

  const bisOptions = [
    { value: "BIS 1", label: "BIS 1" },
    { value: "BIS 2", label: "BIS 2" },
    { value: "BIS 3", label: "BIS 3" },
  ];

  const breakingPointOptions = [
    { value: "Breaking Point 1", label: "Breaking Point 1" },
    { value: "Breaking Point 2", label: "Breaking Point 2" },
    { value: "Breaking Point 3", label: "Breaking Point 3" },
  ];

  const paintOptions = [
    { value: "Paint type 1", label: "Paint type 1" },
    { value: "Paint type 2", label: "Paint type 2" },
    { value: "Paint type 3", label: "Paint type 3" },
  ];

  const colorOptions = [
    { value: "Color type 1", label: "Color type 1" },
    { value: "Color type 2", label: "Color type 2" },
    { value: "Color type 3", label: "Color type 3" },
  ];

  const customStyles = {
    control: (provided) => ({
      ...provided,
      border: "1px solid #e2e8f0",
      boxShadow: "none",
      "&:hover": {
        border: "1px solid #e2e8f0",
      },
    }),
    indicatorSeparator: () => ({
      display: "none",
    }),
  };

  const handleNewProject = () => {
    setShowPopup(true);
  };

  const handleMaterialFormChange = (field, value) => {
    setMaterialFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleComfirmCreate = () => {
    const coreMaterialData = {
      material: materialFormData.material,
      type: materialFormData.type,
      thickness: materialFormData.thickness,
      curvedObject: materialFormData.curvedObject,
      bis: materialFormData.bis,
      breakingPoint: materialFormData.breakingPoint,
    };

    navigate("/create-report", {
      state: {
        orderData: orderFormData,
        materialData: coreMaterialData,
        projectId: generateProjectId(),
      },
    });
    setShowPopup(false);
  };

  const handleClosePopup = () => {
    setShowPopup(false);
    setShowCreateOrderPopup(false);
  };

  const handleCreateOrder = () => {
    setOrderFormData({
      orderDate: null,
      orderingCompany: "",
      deliveryDate: null,
      contactPerson: "",
      deliveryDestination: "",
      shippingClass: null,
      siteName: "",
    });
    setShowCreateOrderPopup(true);
    setShowPopup(false);
  };

  const handleOrderFormChange = (field, value) => {
    setOrderFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleOrderFormSubmit = () => {
    console.log("Order form data:", orderFormData);

    // Close the create order popup
    setShowCreateOrderPopup(false);

    // Reopen the Order Information popup
    setShowPopup(true);
  };

  // Helper function to generate a new project ID
  const generateProjectId = () => {
    return Math.floor(10000000 + Math.random() * 90000000).toString();
  };

  const handleCloseOrderPopup = () => {
    setShowCreateOrderPopup(false);

    // Reopen the Order Information popup
    setShowPopup(true);
  };

  const handleRoughEstimate = () => {
    setShowRoughEstimatePopup(true);
  };

  return (
    <div className="flex flex-col h-screen">
      {/* Action Bar */}
      <div className="sticky top-20 z-10 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0 p-2 border-b bg-white">
        <div className="flex items-start sm:items-center gap-2">
          <button
            onClick={handleNewProject}
            className="w-10 h-10 rounded-full bg-green-500 hover:bg-green-600 text-white flex items-center justify-center shadow-md"
          >
            <FaPlus />
          </button>
          <button
            onClick={handleRoughEstimate}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md shadow-md"
          >
            {t("reportList.roughEstimate")}
          </button>
        </div>
        <div className="relative w-full sm:w-auto">
          <input
            type="text"
            placeholder={t("reportList.searchProject")}
            className="border border-gray-500 rounded-md py-2 px-3 pr-10 w-full sm:w-96"
          />
          <FaSearch className="absolute right-3 top-3 text-gray-400" />
        </div>
      </div>

      {/* Project List */}
      <div className="flex-1 overflow-auto p-4 space-y-4">
        {mockProjects.map((project, index) => (
          <div
            key={index}
            className="bg-white rounded-md shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
          >
            <div className="p-3 flex justify-between items-start">
              {/* Left section: ID and Company */}
              <div className="flex flex-col sm:flex-row sm:gap-32 gap-1">
                <div className="font-bold text-black">{project.id}</div>
                <div className="text-sm text-gray-800">{project.company}</div>
              </div>

              {/* Right section: Date */}
              <div className="text-sm text-black whitespace-nowrap">
                {project.date}
              </div>
            </div>

            {/* Project name */}
            <div className="px-3 pb-3 pt-1">
              <p className="text-sm text-gray-700">{project.projectName}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Order Information Popup */}
      {showPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl p-6">
            <h2 className="text-xl font-bold border-b pb-2 mb-4">
              {t("reportList.orderInformation")}
            </h2>

            <div className="flex mb-8">
              <button
                className="bg-gray-200 hover:bg-gray-300 py-2 px-6 font-bold text-left shadow-md w-1/2"
                onClick={handleCreateOrder}
              >
                {t("reportList.createOrder")}
              </button>
              <div className="w-1/2">
                <Select
                  options={orderOptions}
                  placeholder={t("reportList.selectOrder")}
                  styles={customStyles}
                  className="shadow-md"
                />
              </div>
            </div>

            <h2 className="text-xl font-bold border-b pb-2 mb-4">
              {t("reportList.materialInformation")}
            </h2>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              <Select
                value={{
                  value: materialFormData.material,
                  label: materialFormData.material,
                }}
                onChange={(option) =>
                  handleMaterialFormChange("material", option.value)
                }
                options={materialOptions}
                styles={customStyles}
                className="border"
              />
              <Select
                value={{
                  value: materialFormData.type,
                  label: materialFormData.type,
                }}
                onChange={(option) =>
                  handleMaterialFormChange("type", option.value)
                }
                options={typeOptions}
                styles={customStyles}
                className="border"
              />
              <Select
                value={{
                  value: materialFormData.thickness,
                  label: materialFormData.thickness,
                }}
                onChange={(option) =>
                  handleMaterialFormChange("thickness", option.value)
                }
                options={thicknessOptions}
                styles={customStyles}
                className="border"
              />
              <Select
                value={{
                  value: materialFormData.curvedObject,
                  label: materialFormData.curvedObject,
                }}
                onChange={(option) =>
                  handleMaterialFormChange("curvedObject", option.value)
                }
                options={curvedObjectOptions}
                styles={customStyles}
                className="border"
              />
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <Select
                value={{
                  value: materialFormData.bis,
                  label: materialFormData.bis,
                }}
                onChange={(option) =>
                  handleMaterialFormChange("bis", option.value)
                }
                options={bisOptions}
                styles={customStyles}
                className="border"
              />
              <Select
                value={{
                  value: materialFormData.breakingPoint,
                  label: materialFormData.breakingPoint,
                }}
                onChange={(option) =>
                  handleMaterialFormChange("breakingPoint", option.value)
                }
                options={breakingPointOptions}
                styles={customStyles}
                className="border"
              />
              <Select
                value={{
                  value: materialFormData.paint,
                  label: materialFormData.paint,
                }}
                onChange={(option) =>
                  handleMaterialFormChange("paint", option.value)
                }
                options={paintOptions}
                styles={customStyles}
                className="border"
              />
              <Select
                value={{
                  value: materialFormData.color,
                  label: materialFormData.color,
                }}
                onChange={(option) =>
                  handleMaterialFormChange("color", option.value)
                }
                options={colorOptions}
                styles={customStyles}
                className="border"
              />
            </div>

            <div className="flex justify-center gap-4">
              <button
                onClick={handleClosePopup}
                className="bg-gray-300 hover:bg-gray-400 text-black px-12 py-3 rounded-full"
              >
                {t("reportList.cancel")}
              </button>
              <button
                onClick={handleComfirmCreate}
                className="bg-green-500 hover:bg-green-600 text-white px-12 py-3 rounded-full"
              >
                {t("reportList.ok")}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Order Popup */}
      {showCreateOrderPopup && (
        <OrderEditPopup
          isOpen={showCreateOrderPopup}
          onClose={handleCloseOrderPopup}
          formData={orderFormData}
          onChange={handleOrderFormChange}
          onSubmit={handleOrderFormSubmit}
          title={t("OrderEditPopup.createOrder")}
        />
      )}

      {/* Rough Estimate Popup */}
      {showRoughEstimatePopup && (
        <RoughEstimatePopup
          isOpen={showRoughEstimatePopup}
          onClose={() => setShowRoughEstimatePopup(false)}
          projects={mockProjects}
        />
      )}
    </div>
  );
}

export default ReportListPage;
