import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";

function HoleTypePage() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [holeTypesData, setHoleTypesData] = useState([]);
  const [searchField, setSearchField] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(true);

  const itemsPerPage = 8;

  useEffect(() => {
    const fetchHoleTypes = async () => {
      try {
        const response = await axios.get("/api/fetch/holetype");
        setHoleTypesData(response.data);
      } catch (error) {
        console.error("Error fetching hole type data:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchHoleTypes();
  }, []);

  // ✅ กรองตาม pattern จาก API
  const filtered = searchField
    ? holeTypesData.filter((item) => item.pattern === searchField)
    : holeTypesData;

  const totalPages = Math.ceil(filtered.length / itemsPerPage);
  const paginatedHoleTypes = filtered.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleSearchClick = () => {
    setCurrentPage(0);
  };

  const handleCreateClick = () => {
    navigate("/create-holetype");
  };

  const handleEditClick = (id) => {
    const holeTypeToEdit = holeTypesData.find((item) => item.id === id);

    if (!holeTypeToEdit) {
      Swal.fire("Not found", "Hole type data not found.", "warning");
      return;
    }

    navigate(`/edit-holetype/${id}`, {
      state: { holeTypeToEdit },
    });
  };

  const handleDeleteClick = async (id) => {
    const confirm = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this item?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });

    if (confirm.isConfirmed) {
      try {
        const response = await axios.delete("/api/delete/holetype", {
          data: { id },
        });

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Your data has been deleted.",
            showConfirmButton: false,
            timer: 1000,
          });

          setHoleTypesData((prev) => prev.filter((item) => item.id !== id));
        }
      } catch (error) {
        Swal.fire("Error", "Failed to delete item.", "error");
      }
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-3">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("holeType.hole")}
        </h1>
      </div>

      {/* Header Section */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-3 w-full">
        <div className="flex flex-col sm:flex-row sm:items-center w-full sm:w-auto">
          <div className="flex flex-col sm:flex-row sm:items-center border border-gray-300 rounded-md overflow-hidden sm:mr-3 w-full sm:w-auto">
            <div className="bg-[#4472C4] text-white p-2 min-w-[100px] text-center whitespace-nowrap">
              <span data-testid="text-holeType">{t("holeType.hole")}</span>
            </div>
            <select
              data-testid="select-holeType-search"
              name="selectHoleTypeSearch"
              value={searchField}
              onChange={(e) => setSearchField(e.target.value)}
              className="w-full sm:min-w-[150px] h-10 border-t sm:border-t-0 sm:border-l border-gray-500 rounded-none sm:rounded-r-md shadow-md"
            >
              <option value="">{t("holeType.allType")}</option>
              {/* ✅ Dynamic option จาก pattern ในข้อมูลจริง */}
              {[...new Set(holeTypesData.map((item) => item.pattern))]
                .filter((p) => p) // ตัด empty/null ออก
                .map((p) => (
                  <option key={p} value={p}>
                    {p}
                  </option>
                ))}
            </select>
          </div>
        </div>

        <div className="w-full sm:w-auto">
          <button
            data-testid="button-holeType-search"
            onClick={handleSearchClick}
            className="btn-search w-full sm:w-auto"
          >
            {t("holeType.search")}
          </button>
        </div>

        <div className="w-full sm:w-auto md:ml-auto">
          <button
            data-testid="button-createForm-holeType"
            onClick={handleCreateClick}
            className="btn-create w-full sm:w-auto"
          >
            {t("action.create")}
          </button>
        </div>
      </div>

      {/* Table Section */}
      <div className="relative overflow-x-auto shadow-md">
        <table
          data-testid="table-holeType"
          className="w-full bg-white border-collapse"
        >
          <thead>
            <tr className="bg-[#4472C4] text-white">
              <th className="py-3 px-4 text-left border border-white min-w-[50px]">
                NO
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[120px]">
                {t("holeType.holeCountersunk")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[150px]">
                {t("holeType.priceCountersunk")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[100px]">
                {t("holeType.holeRound1")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[100px]">
                {t("holeType.holeRound2")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[100px]">
                {t("holeType.holeSlotted")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[100px]">
                {t("holeType.priceSlotted")}
              </th>
              <th className="py-3 px-4 text-center border border-white min-w-[80px]">
                {t("holeType.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              [...Array(itemsPerPage)].map((_, i) => (
                <tr
                  key={i}
                  className={i % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  {[...Array(9)].map((_, j) => (
                    <td key={j} className="py-2 px-4 border border-white">
                      <div className="h-4 bg-[#E9EDF9] rounded animate-pulse w-full"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : paginatedHoleTypes.length > 0 ? (
              paginatedHoleTypes.map((holeType, index) => (
                <tr
                  key={holeType.id}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  <td className="py-2 px-4 border border-white">
                    {holeType.id}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {holeType.hole_countersunk}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {holeType.price_countersunk}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {holeType.hole_round_1}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {holeType.hole_round_2}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {holeType.hole_slotted}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {holeType.price_slotted}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    <div className="flex justify-center gap-2">
                      <EditButton
                        dataTestId="button-editForm-holetype"
                        onClick={() => handleEditClick(holeType.id)}
                      />
                      <DeleteButton
                        dataTestId="button-delete-holetype"
                        onClick={() => handleDeleteClick(holeType.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="9" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filtered.length}
      />
    </div>
  );
}

export default HoleTypePage;
