{"common": {"welcome": "Welcome to our app!", "login": "<PERSON><PERSON>", "logout": "Logout", "userid": "User ID", "password": "Password", "useridRequired": "User ID is required", "passwordRequired": "Password is required", "home": "Home"}, "HomePage": {"welcome": "Welcome to Our Platform", "selectOption": "Select one of the options below to get started with your project", "mrpSystem": "MRP System", "reportGenerator": "Report Generator", "chooseModule": "Choose the module that best fits your current workflow needs", "mrpDescription": "Manage resources, inventory, and production planning", "reportDescription": "Generate and export detailed project reports"}, "sidebar": {"userAccount": "User Account", "customerMaster": "Customer Master", "rank": "Rank", "importOrders": "Import Orders", "projectList": "Project List", "material": "Material", "bis": "BIS", "processingFee": "Processing Fee", "coating": "Coating", "holeType": "Hole Type", "remarkUnit": "Remarks in the unit price field", "remarks": "Material remarks text", "ipad": "Ipad"}, "action": {"save": "Save", "cancel": "Cancel", "create": "Create"}, "userAccount": {"userAccountList": "User account list", "searchPlace": "Search UserID or Username", "userID": "User ID", "email": "Email", "username": "Username", "search": "search", "operation": "Operation", "userAdd": "User Management (Add)", "userEdit": "User Management (Edit)", "name": "Name", "branch": "Branch", "department": "Department", "post": "Post", "surName": "Surname", "password": "Password", "confirmPassword": "Confirm Password"}, "customer": {"customer": "Customer Management", "placeholder": "Search by order number, customer abbreviation, or site name", "orderNo": "Order No.", "customerAbbr": "Customer abbreviation", "siteName": "Site name", "person": "Counterpart person in charge", "email": "Email", "rank": "Rank", "customerManagement": "Customer Management (Addition)", "customerEdit": "Customer Management (Edit)", "fixedRate": "Fixed rate adjustment", "recipientContact": "Recipient Contact", "deliveryDestination": "Delivery destination", "shippingClass": "Shipping Class", "holeDrillingPattern": "Hole drilling pattern", "processingPattern": "Processing pattern", "holePattern": "Hole Pattern"}, "rank": {"rankManagement": "Rank Management", "rank": "Rank", "rankAdd": "Rank Management (Addition)", "rankEdit": "Rank Management (Edit)", "sellingPrice": "Selling <PERSON>", "ratePercent": "Rate (%)", "curvedObject": "Curved Object", "cuttingBoard": "Cutting Board", "operation": "Operation"}, "createAndEditRank": {"rankAdd": "Rank Management (Addition)", "rankEdit": "Rank Management (Edit)", "rank": "Rank", "sellingPrice": "Selling <PERSON>", "ratePercent": "Rate (%)", "curvedObject": "Curved Object", "cuttingBoard": "Cutting Board", "operation": "Operation"}, "importOrder": {"orderImport": "Order Import", "orderDate": "Order Date", "orderNo": "OrderNo", "specifiedDeliveryDate": "Specified Delivery Date", "customerAbb": "Customer Abb", "siteName": "SiteName", "recipientContact": "Recipient Contact", "deliveryRoute": "Delivery Route", "deliveryDestinationAbbreviation": "Delivery Dest Abb", "contactName": "Contact Name", "rankName": "Rank Name", "piPercent": "Pi(%)", "operation": "Operation", "upload": "upload", "uploadInfo1": "Select file (. xls , .xlsx, . xlsm )", "uploadInfo2": "Drag files here to upload ( max 20 files )", "searchPlace": "Search by order number, customer abbreviation, or site name"}, "createOrder": {"orderAdd": "Order (Addition)", "orderEdit": "Order (Edit)", "orderDate": "Order Date", "orderNo": "Order No", "specifiedDeliveryDate": "Specified Delivery Date", "customerAbb": "Customer Abb", "siteName": "Site Name", "contactName": "Contact Name", "deliveryDestinationAbbreviation": "Delivery Destination Abbreviation", "recipientContact": "Recipient Contact", "deliveryRoute": "Delivery Route", "rank": "Rank", "pi": "Pi"}, "project": {"projectlist": "Project List", "order": "Order No", "orderDate": "Order Date", "specDate": "Specified delivery date", "customerAbb": "Customer abbreviation", "placeholder": "Search by order number, customer abbreviation, or site name", "customerNo": "Customer No", "siteName": "Site Name", "operation": "Operation", "projectAdd": "Project List (Create)", "repeat": "Repeat", "shipDate": "Scheduled Ship Date", "quoteNo": "Quote No.", "customer": "Customer", "rank": "Rank", "closingDate1": "Closing date 1", "depositCategory": "Deposit Category", "depositAmount0": "0: Normal", "depositAmount1": "1: <PERSON><PERSON><PERSON><PERSON>", "manager": "Manager", "department": "Department", "limitedExpressClass": "Limited express classification", "none": "0: None", "yes": "1: Yes", "deliveryDestination": "Delivery destination", "warehouse": "Warehouse", "transClass": "Transaction Classification", "docsType": "Document Type", "status": "Process completion status", "recipientContact": "Recipient Contact", "cmpdate": "Processing completion date", "process": "Process", "material": "Material", "classification": "Classification", "prodCode": "Product Code", "retailPrce": "Retail price", "remarks": "Remarks", "specDeliDate": "Specified delivery date", "pdName": "Product name", "tax": "Tax (%)", "odQty": "Order Quantity", "allocatedQty": "Allocated Quantity", "odPrce": "Order price", "unitUp": " Unit price update", "odAmount": "Order amount", "odTaxAmount": "Order consumption tax amount", "schShipDate": "Scheduled Ship Date", "unit": "Unit", "csUnitPrce": "Cost unit price", "csAmount": "Cost Amount", "soldOut": "Sold out", "shipmentComplete": "Shipment complete", "stickyNote": "Sticky note", "arrangementCode": "Arrangement code", "supplier": "Supplier", "total": "Amount / Tax / Total", "dataEntryPerson": "Data entry person", "deliveryCourse": "Delivery Course", "totalCost": "Cost / gross profit / gross profit rate", "Shirring": "<PERSON><PERSON>", "Processing": "Processing", "Vendor": "<PERSON><PERSON><PERSON>", "Welding": "Welding", "Finishing": "Finishing", "Color": "Color", "Rust Prevention": "Rust Prevention", "Vibration": "Vibration", "Completion": "Completion"}, "material": {"materialManagement": "Material Management", "material": "Material", "materialAdd": " Material Management (Additional)", "materialEdit": "Material Management (Edit)", "materialName": "Material Name", "matetialPrice": "Price", "operation": "Operation", "maxLen": "Maximum length", "order": "NO", "allthicknesses": "All thicknesses", "code": "Code", "explanation": "explanation", "display": "display", "thickness": "<PERSON><PERSON><PERSON><PERSON>", "maxLit": "Maximum limit", "curObj": "Curved object/Cut Board", "unitPrc": "unit price"}, "bis": {"bis": "Service Management", "bisAdd": "Service Management (Additional)", "No": "NO", "materialName": "Material Name", "screwType": "<PERSON><PERSON>", "maxLen": "Maximum length", "allThickn": "All thicknesses", "explanation": "Explanation", "price": "Price", "color": "Color", "thickness": "<PERSON><PERSON><PERSON><PERSON>", "maxLimit": "Maximum limit", "curvObj": "Curved objects / cut sheets", "unitPrce": "Unit price", "operation": "Operation"}, "processingFee": {"processingFee": "Processing Fee Management", "processingFeeAdd": "Processing Fee Management (Additional)", "processingFeeEdit": "Processing Fee Management (Edit)", "processingItems": "Processing Items", "calculationCriteria": "Calculation Criteria", "lowestPrice": "Lowest price", "priceByMaterial": "Price by material", "pattern": "Pattern", "operation": "Operation"}, "coating": {"coating": "Coating Management", "coatingAdd": "Coating Management (Additional)", "coatingEdit": "Coating Management (Edit)", "No": "NO", "paintType": "Paint type", "colorType": "Color types", "cosmeticSurface": "Cosmetic surface", "priceClassification": "Price classification", "priceByMaterial": "Price by material", "operation": "Operation"}, "holeType": {"holeAdd": "Hole Type (Additional)", "hole": "Hole Type", "allType": "-- All --", "pattern1": "Pattern 1", "pattern2": "Pattern 2", "holeCountersunk": "Hole Countersunk", "priceCountersunk": "Price Countersunk", "holeRound1": "Hole Round 1", "holeRound2": "Hole Round 2", "holeSlotted": "Hole Slotted", "priceSlotted": "Price Slotted", "operation": "Operation", "create": "Create", "materialType": "Material Type", "colorTypes": "Color types", "size": "Size", "search": "Search", "unit": "Unit", "pattern": "Pattern"}, "remark": {"remarkUnit": "Remarks in the unit price field", "remarkMate": "Material remarks text", "material": "Material :", "code": "Code :", "unitPrce": "Unit Price :", "unit": "Unit :", "remark": "Remark :", "remarkPlace": "Please enter any additional information or special notes"}, "reportList": {"reportList": "Report List", "searchProject": "Search Project", "orderInformation": "Order Information", "createOrder": "Create an Order", "selectOrder": "Please select an order", "materialInformation": "Material Information", "roughEstimate": "Rough Estimate", "cancel": "Cancel", "ok": "OK"}, "RoughEstimatePopup": {"total": "Total", "selected": "Selected", "search": "Search...", "cancel": "Cancel", "ok": "OK"}, "OrderEditPopup": {"createOrder": "Create an Order", "editOrder": "Edit Order", "orderDate": "Order Date", "orderingCompany": "Ordering Company", "deliveryDate": "Desired delivery date", "contactPerson": "Contact Person", "deliveryDestination": "Delivery Destination", "siteName": "Site Name", "shippingClass": "Shipping Class", "shippingClassPlaceholder": "Select shipping class", "cancel": "Cancel", "ok": "OK"}, "CreateReportList": {"orderingCompanyName": "Ordering Company Name", "contactPerson": "Contact Person", "deliveryDestination": "Delivery Destination", "siteName": "Site Name (subject)", "scheduledShipDate": "Scheduled Ship Date", "shippingClass": "Shipping Class", "appearance": "Appearance", "yarnWidth": "Yarn Width", "length": "Length", "quantity": "Quantity", "unitPrice": "Unit Price", "amount": "Amount", "orderInformation": "Order Information", "materialInformation": "Material Information", "ok": "OK", "cancel": "Cancel", "crossSection": "Cross Section", "boxBending": "Box Bending"}, "DrawPage": {"pattern12": "Pattern 1,2", "pattern3": "Pattern 3", "pattern4": "Pattern 4", "pattern5": "Pattern 5", "pattern6": "Pattern 6", "pattern7": "Pattern 7", "squareAndRoundHoles": "Square and Round Holes", "projectNo": "Project No.", "threadWidth": "<PERSON><PERSON><PERSON>", "quantity": "Quantity", "unitPrice": "Unit Price", "amount": "Amount", "length": "Length", "cancel": "Cancel", "ok": "OK"}, "Toolbar": {"adjust": "Adjust", "adjustDes1": "Adjust the standard parameter values.", "adjustDes2": "When you're done, click the 'Adjust' button."}}