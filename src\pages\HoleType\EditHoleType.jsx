import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

function EditHoleType() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const { holeTypeToEdit } = location.state || {};

  const [formData, setFormData] = useState({
    hole_countersunk: "",
    price_countersunk: "",
    hole_round_1: "",
    hole_round_2: "",
    hole_slotted: "",
    price_slotted: "",
    pattern: "",
  });

  // Remove the old options since we're now using the same structure as HoleTypePage

  const [id, setId] = useState(null);

  // Load data when component mounts
  useEffect(() => {
    if (holeTypeToEdit) {
      setFormData({
        hole_countersunk: holeTypeToEdit.hole_countersunk || "",
        price_countersunk: holeTypeToEdit.price_countersunk || "",
        hole_round_1: holeTypeToEdit.hole_round_1 || "",
        hole_round_2: holeTypeToEdit.hole_round_2 || "",
        hole_slotted: holeTypeToEdit.hole_slotted || "",
        price_slotted: holeTypeToEdit.price_slotted || "",
        pattern: holeTypeToEdit.pattern || "",
      });
      setId(holeTypeToEdit.id);
    }
  }, [holeTypeToEdit]);

  const handleChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleUpdate = async () => {
    const { hole_countersunk, price_countersunk, hole_round_1, hole_round_2, hole_slotted, price_slotted, pattern } =
      formData;

    if (
      !hole_countersunk.toString().trim() ||
      !price_countersunk.toString().trim() ||
      !hole_round_1.toString().trim() ||
      !hole_round_2.toString().trim() ||
      !hole_slotted.toString().trim() ||
      !price_slotted.toString().trim() ||
      !pattern.trim()
    ) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const payload = {
        id,
        hole_countersunk,
        price_countersunk,
        hole_round_1,
        hole_round_2,
        hole_slotted,
        price_slotted,
        pattern,
      };

      const response = await axios.put("/api/edit/holetype", payload);

      if (response.status === 200) {
        await Swal.fire({
          icon: "success",
          title: "Updated successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/hole-type");
      }
    } catch (error) {
      console.error("Error updating data:", error);
      Swal.fire({
        icon: "error",
        title: "Error updating data",
        text: error.response?.data?.error || "Unable to update the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/hole-type");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1 data-testid="text-holeType-edit" className="text-2xl font-bold mb-6">
        {t("holeType.hole")}
      </h1>

      <div className="mb-8 p-6 border border-gray-300 rounded-md shadow-sm relative">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
          {/* Hole Countersunk */}
          <div>
            <label
              data-testid="text-editFormHoleType-holeCountersunk"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.holeCountersunk")}
            </label>
            <input
              data-testid="input-editFormHoleType-holeCountersunk"
              name="hole_countersunk"
              type="number"
              value={formData.hole_countersunk}
              onChange={(e) => handleChange("hole_countersunk", e.target.value)}
              placeholder="0"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Price Countersunk */}
          <div>
            <label
              data-testid="text-editFormHoleType-priceCountersunk"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.priceCountersunk")}
            </label>
            <input
              data-testid="input-editFormHoleType-priceCountersunk"
              name="price_countersunk"
              type="number"
              value={formData.price_countersunk}
              onChange={(e) => handleChange("price_countersunk", e.target.value)}
              placeholder="0"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Hole Round 1 */}
          <div>
            <label
              data-testid="text-editFormHoleType-holeRound1"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.holeRound1")}
            </label>
            <input
              data-testid="input-editFormHoleType-holeRound1"
              name="hole_round_1"
              type="number"
              value={formData.hole_round_1}
              onChange={(e) => handleChange("hole_round_1", e.target.value)}
              placeholder="0"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Hole Round 2 */}
          <div>
            <label
              data-testid="text-editFormHoleType-holeRound2"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.holeRound2")}
            </label>
            <input
              data-testid="input-editFormHoleType-holeRound2"
              name="hole_round_2"
              type="number"
              value={formData.hole_round_2}
              onChange={(e) => handleChange("hole_round_2", e.target.value)}
              placeholder="0"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Hole Slotted */}
          <div>
            <label
              data-testid="text-editFormHoleType-holeSlotted"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.holeSlotted")}
            </label>
            <input
              data-testid="input-editFormHoleType-holeSlotted"
              name="hole_slotted"
              type="number"
              value={formData.hole_slotted}
              onChange={(e) => handleChange("hole_slotted", e.target.value)}
              placeholder="0"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Price Slotted */}
          <div>
            <label
              data-testid="text-editFormHoleType-priceSlotted"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.priceSlotted")}
            </label>
            <input
              data-testid="input-editFormHoleType-priceSlotted"
              name="price_slotted"
              type="number"
              value={formData.price_slotted}
              onChange={(e) => handleChange("price_slotted", e.target.value)}
              placeholder="0"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Pattern */}
          <div>
            <label
              data-testid="text-editFormHoleType-pattern"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.pattern")}
            </label>
            <input
              data-testid="input-editFormHoleType-pattern"
              name="pattern"
              type="text"
              value={formData.pattern}
              onChange={(e) => handleChange("pattern", e.target.value)}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 my-5">
        <SaveButton
          dataTestId="button-editFormHoleType-saveHoleType"
          onClick={handleUpdate}
        />
        <CancelButton
          dataTestId="button-editFormHoleType-cancelHoleType"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default EditHoleType;
