import { useState, useRef, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import LanguageSelector from "../../components/LanguageSelector";

import OrderEditPopup from "../../components/Popups/OrderEditPopup";
import Select from "react-select";
import NumericKeypad from "../../components/DrawTools/Popups/NumericKeypad";

// React-icons
import {
  FaArrowLeft,
  FaPencilAlt,
  FaFilePdf,
  FaPrint,
  FaSave,
  FaPlus,
  FaCopy,
  FaTrash,
} from "react-icons/fa";

function CreateReportList() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const tableRef = useRef(null);
  const [originalOrderFormData, setOriginalOrderFormData] = useState(null);
  const [projectData, setProjectData] = useState({
    projectId: "12345678",
    orderingCompany: "Sample Company",
    contactPerson: "John Doe",
    deliveryDestination: "Tokyo, Japan",
    siteName: "Project Alpha",
    scheduledShipDate: "2023/12/31",
    shippingClass: "Shipping F",
  });
  const [showEditPopup, setShowEditPopup] = useState(false);
  const [showCrossSectionPopup, setShowCrossSectionPopup] = useState(false);
  const [orderFormData, setOrderFormData] = useState({
    orderDate: new Date(),
    orderingCompany: "",
    deliveryDate: new Date(),
    contactPerson: "",
    deliveryDestination: "",
    shippingClass: null,
    siteName: "",
  });
  const [showMaterialEditPopup, setShowMaterialEditPopup] = useState(false);
  const [materialFormData, setMaterialFormData] = useState({
    material: "Aluminum",
    type: "TS-1N",
    thickness: "t-1.0",
    curvedObject: "Magmono",
    bis: "bis",
    breakingPoint: "Breaking Point 1",
  });
  const [selectedRowIndex, setSelectedRowIndex] = useState(null);
  const [showRowOptionsPopup, setShowRowOptionsPopup] = useState(false);

  // Add state for cell editing
  const [editingCell, setEditingCell] = useState(null);
  const [editingValue, setEditingValue] = useState("");
  const [showKeypad, setShowKeypad] = useState(false);

  // Function to handle cell click for editing
  const handleCellClick = (field, value, isNumeric = true) => {
    setEditingCell(field);
    setEditingValue(value);

    if (isNumeric) {
      setShowKeypad(true);
    }
  };

  // Function to handle keypad confirmation
  const handleKeypadConfirm = (value) => {
    // Update the value in your data structure
    // This is a placeholder - you'll need to update your actual data structure
    console.log(`Updating ${editingCell} to ${value}`);

    // Close the keypad
    setShowKeypad(false);
    setEditingCell(null);
  };

  // Initialize project data from location state if available
  useEffect(() => {
    if (location.state?.orderData && location.state?.projectId) {
      const { orderData, materialData, projectId } = location.state;

      const initialForm = {
        orderDate: orderData.orderDate || new Date(),
        orderingCompany: orderData.orderingCompany || "",
        contactPerson: orderData.contactPerson || "",
        deliveryDestination: orderData.deliveryDestination || "",
        siteName: orderData.siteName || "",
        deliveryDate: orderData.deliveryDate || new Date(),
        shippingClass: orderData.shippingClass || {
          value: "shipping-f",
          label: "Shipping F",
        },
      };

      setProjectData({
        projectId: projectId,
        orderingCompany: orderData.orderingCompany || "",
        contactPerson: orderData.contactPerson || "",
        deliveryDestination: orderData.deliveryDestination || "",
        siteName: orderData.siteName || "",
        scheduledShipDate: initialForm.deliveryDate
          .toISOString()
          .split("T")[0]
          .replace(/-/g, "/"),
        shippingClass: initialForm.shippingClass.label,
      });

      setOrderFormData(initialForm);
      setOriginalOrderFormData(initialForm);

      // Update material form data if passed from ReportListPage
      if (materialData) {
        setMaterialFormData({
          material: materialData.material || "",
          type: materialData.type || "",
          thickness: materialData.thickness || "",
          curvedObject: materialData.curvedObject || "",
          bis: materialData.bis || "",
          breakingPoint: materialData.breakingPoint || "",
        });
      }
    }
  }, [location.state]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (tableRef.current && !tableRef.current.contains(event.target)) {
        setSelectedRowIndex(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleGoBack = () => {
    navigate("/report-list");
  };

  const handleEditOrder = () => {
    setShowEditPopup(true);
  };

  const handleClosePopup = () => {
    if (originalOrderFormData) {
      setOrderFormData(originalOrderFormData);
    }
    setShowEditPopup(false);
  };

  const handleOrderFormChange = (field, value) => {
    setOrderFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleOrderFormSubmit = () => {
    // Update project data with form values
    setProjectData({
      ...projectData,
      orderDate: orderFormData.orderDate,
      orderingCompany: orderFormData.orderingCompany,
      contactPerson: orderFormData.contactPerson,
      deliveryDestination: orderFormData.deliveryDestination,
      siteName: orderFormData.siteName,
      scheduledShipDate: orderFormData.deliveryDate
        .toISOString()
        .split("T")[0]
        .replace(/-/g, "/"),
      shippingClass: orderFormData.shippingClass?.label || "",
    });

    // Close the popup
    setShowEditPopup(false);
  };

  const handleAddMaterial = () => {
    setShowCrossSectionPopup(true);
  };

  const handleCrossSection = () => {
    navigate("/draw-page");
    setShowCrossSectionPopup(false);
  };

  const handleBoxBending = () => {
    setShowCrossSectionPopup(false);
  };

  const handleEditMaterial = () => {
    setShowMaterialEditPopup(true);
  };

  const handleCloseMaterialPopup = () => {
    setShowMaterialEditPopup(false);
  };

  const handleMaterialFormChange = (field, value) => {
    setMaterialFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleMaterialFormSubmit = () => {
    // Update material data with form values
    // Close the popup
    setShowMaterialEditPopup(false);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-200">
      {/* Header */}
      <div className="bg-green-600 text-white flex justify-between items-center px-2">
        <div className="flex items-center">
          <button
            onClick={handleGoBack}
            className="mr-4 hover:bg-green-700 p-2 rounded-md"
          >
            <FaArrowLeft className="w-6 h-6" />
          </button>
          <LanguageSelector />
        </div>
        <div>
          <h1 className="text-xl font-semibold">
            New Project No.{projectData.projectId}
          </h1>
        </div>
        <div className="flex items-center space-x-4">
          <button
            className="hover:bg-green-700 p-2 rounded-md"
            onClick={handleEditOrder}
          >
            <FaPencilAlt className="w-6 h-6" />
          </button>
          <button className="hover:bg-green-700 p-2 rounded-md">
            <FaFilePdf className="w-6 h-6" />
          </button>
          <button className="hover:bg-green-700 p-2 rounded-md">
            <FaPrint className="w-6 h-6" />
          </button>
          <button className="hover:bg-green-700 p-2 rounded-md">
            <FaSave className="w-6 h-6" />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-4">
        {/* Ordering company name */}
        <div className="bg-white rounded-md shadow-sm mb-1 max-w-6xl mx-auto">
          <div className="grid grid-cols-3 px-4 py-1 text-base">
            <div className="col-span-3">
              {t("CreateReportList.orderingCompanyName")}
            </div>
            <div className="col-start-2 col-span-2 font-bold">
              {projectData.orderingCompany}
            </div>
          </div>
        </div>

        {/* Contact Person and Delivery Destination */}
        <div className="mb-1 max-w-6xl mx-auto">
          <div className="bg-white rounded-md shadow-sm mb-1 grid grid-cols-5">
            <div className="col-span-2 px-4 py-1 text-base border-r">
              <div>{t("CreateReportList.contactPerson")}</div>
              <div className="font-bold text-right">
                {projectData.contactPerson}
              </div>
            </div>
            <div className="col-span-3 px-4 py-1 text-base">
              <div>{t("CreateReportList.deliveryDestination")}</div>
              <div className="font-bold text-right">
                {projectData.deliveryDestination}
              </div>
            </div>
          </div>
        </div>

        {/* Site name */}
        <div className="bg-white rounded-md shadow-sm mb-1 max-w-6xl mx-auto">
          <div className="grid grid-cols-3 px-4 py-1 text-base">
            <div className="col-span-3">{t("CreateReportList.siteName")}</div>
            <div className="col-start-2 col-span-2 font-bold">
              {projectData.siteName}
            </div>
          </div>
        </div>

        {/* Scheduled ship date and shipping class */}
        <div className="mb-1 max-w-6xl mx-auto">
          <div className="bg-white rounded-md shadow-sm mb-1 grid grid-cols-5">
            <div className="col-span-2 px-4 py-1 text-base border-r">
              <div>{t("CreateReportList.scheduledShipDate")}</div>
              <div className="font-bold text-right">
                {projectData.scheduledShipDate}
              </div>
            </div>
            <div className="col-span-3 px-4 py-1 text-base">
              <div>{t("CreateReportList.shippingClass")}</div>
              <div className="font-bold text-right">
                {projectData.shippingClass}
              </div>
            </div>
          </div>
        </div>

        {/* Material Information Section */}
        <div className="bg-white rounded-md shadow-sm mb-1 max-w-6xl mx-auto">
          <div className="p-4">
            {/* Material tags and buttons */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4 sm:gap-0">
              <div className="grid grid-cols-3 sm:grid-cols-6 w-full sm:w-auto">
                <div className="bg-gray-200 px-2 py-2 border border-gray-300 text-center text-xs sm:text-sm">
                  <span>{materialFormData.material}</span>
                </div>
                <div className="bg-gray-200 px-2 py-2 border border-gray-300 text-center text-xs sm:text-sm">
                  <span>{materialFormData.type}</span>
                </div>
                <div className="bg-gray-200 px-2 py-2 border border-gray-300 text-center text-xs sm:text-sm">
                  <span>{materialFormData.thickness}</span>
                </div>
                <div className="bg-gray-200 px-2 py-2 border border-gray-300 text-center text-xs sm:text-sm">
                  <span>{materialFormData.curvedObject}</span>
                </div>
                <div className="bg-gray-200 px-2 py-2 border border-gray-300 text-center text-xs sm:text-sm">
                  <span>{materialFormData.bis}</span>
                </div>
                <div className="bg-gray-200 px-2 py-2 border border-gray-300 text-center text-xs sm:text-sm">
                  <span>{materialFormData.breakingPoint}</span>
                </div>
              </div>
              <div className="flex gap-6 mt-2 sm:mt-0 self-end sm:self-auto">
                <button
                  className="text-green-600 hover:text-green-700"
                  onClick={handleEditMaterial}
                >
                  <FaPencilAlt className="w-5 h-5" />
                </button>
                <button
                  className="bg-green-600 hover:bg-green-700 text-white rounded-full flex items-center justify-center w-7 h-7"
                  onClick={handleAddMaterial}
                >
                  <FaPlus />
                </button>
              </div>
            </div>

            {/* Table Pattern and Information Data */}
            <div className="overflow-x-auto">
              <table
                ref={tableRef}
                className="min-w-full border-collapse border border-gray-300 text-xs sm:text-sm md:text-base"
              >
                <thead>
                  <tr>
                    <th className="border border-gray-300 p-1 text-center w-1/3">
                      {t("CreateReportList.appearance")}
                    </th>
                    <th className="border border-gray-300 p-1 text-center">
                      {t("CreateReportList.yarnWidth")}
                    </th>
                    <th className="border border-gray-300 p-1 text-center">
                      {t("CreateReportList.length")}
                    </th>
                    <th className="border border-gray-300 p-1 text-center">
                      {t("CreateReportList.quantity")}
                    </th>
                    <th className="border border-gray-300 p-1 text-center">
                      {t("CreateReportList.unitPrice")}
                    </th>
                    <th className="border border-gray-300 p-1 text-center">
                      {t("CreateReportList.amount")}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    className={`cursor-pointer transition-colors duration-200 ${
                      selectedRowIndex === 0 ? "border-2 border-blue-500" : ""
                    }`}
                  >
                    <td
                      onClick={() => {
                        setSelectedRowIndex(0);
                        setShowRowOptionsPopup(true);
                      }}
                      className="border border-gray-300 p-1 text-center"
                    >
                      <img
                        src="#"
                        alt="Pattern"
                        className="mx-auto h-20 sm:h-28 md:h-32"
                      />
                    </td>
                    <td
                      className="border border-gray-300 p-1 text-center align-bottom"
                      onClick={() => handleCellClick("yarnWidth", "93.2")}
                    >
                      <span>93.2</span>
                    </td>
                    <td
                      className="border border-gray-300 p-1 text-center align-bottom"
                      onClick={() => handleCellClick("length", "1000")}
                    >
                      <span>1000</span>
                    </td>
                    <td className="border border-gray-300 p-1 text-center h-24">
                      <div className="flex flex-col justify-between h-full">
                        <div className="flex-grow flex items-center justify-center">
                          <span>NO.</span>
                          <span>{selectedRowIndex + 1}</span>
                        </div>
                        <div onClick={() => handleCellClick("quantity", "1")}>
                          <span>1</span>
                        </div>
                      </div>
                    </td>
                    <td
                      className="border border-gray-300 p-1 text-center align-bottom"
                      onClick={() => handleCellClick("unitPrice", "1660")}
                    >
                      <span>1660</span>
                    </td>
                    <td
                      className="border border-gray-300 p-1 text-center align-bottom"
                      onClick={() => handleCellClick("amount", "1660")}
                    >
                      <span>1660</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Row Options Popup */}
            {showRowOptionsPopup && (
              <div
                className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]"
                onClick={() => setShowRowOptionsPopup(false)}
              >
                <div
                  className="w-full max-w-xs overflow-hidden rounded-lg"
                  onClick={(e) => e.stopPropagation()}
                >
                  <button
                    className="w-full py-3 px-4 flex items-center gap-2 text-white bg-green-600 hover:bg-green-700 transition-colors font-semibold"
                    onClick={() => {
                      console.log("Create different lengths");
                      setShowRowOptionsPopup(false);
                    }}
                  >
                    <FaPlus className="w-5 h-5" />
                    <span className="pl-2">Create different lengths</span>
                  </button>
                  <button
                    className="w-full py-3 px-4 flex items-center gap-2 text-white bg-green-600 hover:bg-green-700 transition-colors font-semibold border-t border-b border-gray-300"
                    onClick={() => {
                      console.log("Copy row", selectedRowIndex);
                      setShowRowOptionsPopup(false);
                    }}
                  >
                    <FaCopy className="w-5 h-5" />
                    <span className="pl-2">Copy</span>
                  </button>
                  <button
                    className="w-full py-3 px-4 flex items-center gap-2 text-white bg-green-600 hover:bg-green-700 transition-colors font-semibold"
                    onClick={() => {
                      console.log("Delete row", selectedRowIndex);
                      setShowRowOptionsPopup(false);
                    }}
                  >
                    <FaTrash className="w-5 h-5" />
                    <span className="pl-2">Delete</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Order Edit Popup */}
      {showEditPopup && (
        <OrderEditPopup
          isOpen={showEditPopup}
          onClose={handleClosePopup}
          formData={orderFormData}
          onChange={handleOrderFormChange}
          onSubmit={handleOrderFormSubmit}
          title={t("OrderEditPopup.editOrder")}
        />
      )}

      {/* Cross Section Popup */}
      {showCrossSectionPopup && (
        <div
          onClick={() => setShowCrossSectionPopup(false)}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]"
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="w-full max-w-xs overflow-hidden rounded-lg"
          >
            <button
              className="w-full py-3 px-4 text-center text-white bg-green-600 hover:bg-green-700 transition-colors font-semibold"
              onClick={handleCrossSection}
            >
              {t("CreateReportList.crossSection")}
            </button>
            <button
              className="w-full py-3 px-4 text-center text-white bg-green-600 hover:bg-green-700 transition-colors font-semibold"
              onClick={handleBoxBending}
            >
              {t("CreateReportList.boxBending")}
            </button>
          </div>
        </div>
      )}

      {/* Material Edit Popup */}
      {showMaterialEditPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div
            onClick={(e) => e.stopPropagation()}
            className="bg-white rounded-lg shadow-lg w-full max-w-3xl p-6"
          >
            {/* Order Information Section */}
            <h2 className="text-xl font-bold mb-2">
              {t("CreateReportList.orderInformation")}
            </h2>
            <div className="border-t border-gray-300 mb-4"></div>

            <div className="bg-gray-100 p-4 mb-6 shadow-sm">
              <div className="flex items-center">
                <span className="text-lg font-bold mr-2">
                  {projectData.projectId}
                </span>
                <span className="mr-2">|</span>
                <span className="text-lg">
                  (ML) <span className="underline">{projectData.siteName}</span>
                </span>
              </div>
            </div>

            {/* Material Information Section */}
            <h2 className="text-xl font-bold mb-2">
              {t("CreateReportList.materialInformation")}
            </h2>
            <div className="border-t border-gray-300 mb-4"></div>

            <div className="space-y-4">
              <div className="flex flex-wrap justify-center gap-x-4 gap-y-4">
                <div className="w-full md:w-52">
                  <Select
                    value={{
                      value: materialFormData.material,
                      label: materialFormData.material,
                    }}
                    onChange={(option) =>
                      handleMaterialFormChange("material", option.value)
                    }
                    options={[
                      { value: "Aluminum", label: "Aluminum" },
                      { value: "Steel", label: "Steel" },
                      { value: "Plastic", label: "Plastic" },
                    ]}
                    className="w-full"
                    classNamePrefix="select"
                  />
                </div>

                <div className="w-full md:w-52">
                  <Select
                    value={{
                      value: materialFormData.type,
                      label: materialFormData.type,
                    }}
                    onChange={(option) =>
                      handleMaterialFormChange("type", option.value)
                    }
                    options={[
                      { value: "TS-1N", label: "TS-1N" },
                      { value: "TS-2N", label: "TS-2N" },
                      { value: "TS-3N", label: "TS-3N" },
                    ]}
                    className="w-full"
                    classNamePrefix="select"
                  />
                </div>

                <div className="w-full md:w-52">
                  <Select
                    value={{
                      value: materialFormData.thickness,
                      label: materialFormData.thickness,
                    }}
                    onChange={(option) =>
                      handleMaterialFormChange("thickness", option.value)
                    }
                    options={[
                      { value: "t-1.0", label: "t-1.0" },
                      { value: "t-1.5", label: "t-1.5" },
                    ]}
                    className="w-full"
                    classNamePrefix="select"
                  />
                </div>

                <div className="w-full md:w-52">
                  <Select
                    value={{
                      value: materialFormData.curvedObject,
                      label: materialFormData.curvedObject,
                    }}
                    onChange={(option) =>
                      handleMaterialFormChange("curvedObject", option.value)
                    }
                    options={[
                      { value: "Curved Object 1", label: "Curved Object 1" },
                      { value: "Curved Object 2", label: "Curved Object 2" },
                      { value: "Curved Object 3", label: "Curved Object 3" },
                    ]}
                    className="w-full"
                    classNamePrefix="select"
                  />
                </div>

                <div className="w-full md:w-52">
                  <Select
                    value={{
                      value: materialFormData.bis,
                      label: materialFormData.bis,
                    }}
                    onChange={(option) =>
                      handleMaterialFormChange("bis", option.value)
                    }
                    options={[
                      { value: "BIS 1", label: "BIS 1" },
                      { value: "BIS 2", label: "BIS 2" },
                      { value: "BIS 3", label: "BIS 3" },
                    ]}
                    className="w-full"
                    classNamePrefix="select"
                  />
                </div>

                <div className="w-full md:w-52">
                  <Select
                    value={{
                      value: materialFormData.breakingPoint,
                      label: materialFormData.breakingPoint,
                    }}
                    onChange={(option) =>
                      handleMaterialFormChange("breakingPoint", option.value)
                    }
                    options={[
                      { value: "Breaking Point 1", label: "Breaking Point 1" },
                      { value: "Breaking Point 2", label: "Breaking Point 2" },
                      { value: "Breaking Point 3", label: "Breaking Point 3" },
                    ]}
                    className="w-full"
                    classNamePrefix="select"
                  />
                </div>
              </div>

              <div className="flex justify-center space-x-6 mt-6">
                <button
                  className="bg-gray-300 hover:bg-gray-400 text-black px-12 py-3 rounded-full"
                  onClick={handleCloseMaterialPopup}
                >
                  {t("CreateReportList.cancel")}
                </button>
                <button
                  className="bg-green-500 hover:bg-green-600 text-white px-12 py-3 rounded-full"
                  onClick={handleMaterialFormSubmit}
                >
                  {t("CreateReportList.ok")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Numeric Keypad for editing numeric cells */}
      {showKeypad && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-30">
          <NumericKeypad
            initialValue={editingValue}
            onConfirm={handleKeypadConfirm}
            onCancel={() => {
              setShowKeypad(false);
              setEditingCell(null);
            }}
          />
        </div>
      )}
    </div>
  );
}

export default CreateReportList;
