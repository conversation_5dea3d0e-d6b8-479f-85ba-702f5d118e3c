import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";
import CustomSelect from "../../components/CustomSelect";

// React-icons
import { FaPlus, FaTimes } from "react-icons/fa";

function CreateHoleType() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [formData, setFormData] = useState([
    {
      hole_countersunk: "",
      price_countersunk: "",
      hole_round_1: "",
      hole_round_2: "",
      hole_slotted: "",
      price_slotted: "",
      pattern: "",
    },
  ]);

  const patternOptions = [
    { value: "Square", label: "Square" },
    { value: "Round", label: "Round" },
    { value: "Rectangular", label: "Rectangular" },
    { value: "Hexagonal", label: "Hexagonal" },
    { value: "Custom", label: "Custom" },
  ];

  const handleChange = (index, field, value) => {
    const updatedSets = [...formData];
    updatedSets[index][field] = value;
    setFormData(updatedSets);
  };

  const handleAddSet = () => {
    setFormData([
      ...formData,
      {
        hole_countersunk: "",
        price_countersunk: "",
        hole_round_1: "",
        hole_round_2: "",
        hole_slotted: "",
        price_slotted: "",
        pattern: "",
      },
    ]);
  };

  const handleDeleteSet = (index) => {
    const updatedSets = [...formData];
    updatedSets.splice(index, 1);
    setFormData(updatedSets);
  };

  const handleSave = async (event) => {
    event.preventDefault();

    for (const item of formData) {
      if (
        !item.hole_countersunk.toString().trim() ||
        !item.price_countersunk.toString().trim() ||
        !item.hole_round_1.toString().trim() ||
        !item.hole_round_2.toString().trim() ||
        !item.hole_slotted.toString().trim() ||
        !item.price_slotted.toString().trim() ||
        !item.pattern.trim()
      ) {
        await Swal.fire({
          icon: "warning",
          title: "Please fill in all required fields.",
          confirmButtonText: "OK",
        });
        return;
      }
    }

    try {
      const response = await axios.post("/api/create/holetype", formData);

      if (response.status === 200 || response.status === 201) {
        await Swal.fire({
          icon: "success",
          title: t("Save successful!"),
          showConfirmButton: false,
          timer: 1500,
        });
        navigate("/hole-type");
      } else {
        await Swal.fire({
          icon: "error",
          title: t("Save failed"),
          text: response.statusText || "",
          confirmButtonText: "OK",
        });
      }
    } catch (error) {
      console.error("Error saving hole types:", error);
      await Swal.fire({
        icon: "error",
        title: t("Save failed due to network error."),
        text: error.message,
        confirmButtonText: "OK",
      });
    }
  };

  const handleCancel = () => {
    navigate("/hole-type");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1
        data-testid="text-holeType-create"
        className="text-2xl font-bold mb-6"
      >
        {t("holeType.holeAdd")}
      </h1>

      {formData.map((set, index) => (
        <div
          key={index}
          className="mb-8 p-6 border border-gray-300 rounded-md shadow-sm relative"
        >
          {/* Delete button - only show if there's more than one form */}
          {formData.length > 1 && (
            <button
              data-testid="button-createFormHoleType-deleteForm"
              name="buttonDeleteFormHoleType"
              type="button"
              onClick={() => handleDeleteSet(index)}
              className="absolute top-2 right-2 text-red-500 hover:text-red-700"
            >
              <FaTimes size={20} />
            </button>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            {/* Hole Countersunk */}
            <div>
              <label
                data-testid="text-createFormHoleType-holeCountersunk"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.holeCountersunk")}
              </label>
              <input
                data-testid="input-createFormHoleType-holeCountersunk"
                name="hole_countersunk"
                type="number"
                value={set.hole_countersunk}
                onChange={(e) => handleChange(index, "hole_countersunk", e.target.value)}
                placeholder="0"
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Price Countersunk */}
            <div>
              <label
                data-testid="text-createFormHoleType-priceCountersunk"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.priceCountersunk")}
              </label>
              <input
                data-testid="input-createFormHoleType-priceCountersunk"
                name="price_countersunk"
                type="number"
                value={set.price_countersunk}
                onChange={(e) => handleChange(index, "price_countersunk", e.target.value)}
                placeholder="0"
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Hole Round 1 */}
            <div>
              <label
                data-testid="text-createFormHoleType-holeRound1"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.holeRound1")}
              </label>
              <input
                data-testid="input-createFormHoleType-holeRound1"
                name="hole_round_1"
                type="number"
                value={set.hole_round_1}
                onChange={(e) => handleChange(index, "hole_round_1", e.target.value)}
                placeholder="0"
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Hole Round 2 */}
            <div>
              <label
                data-testid="text-createFormHoleType-holeRound2"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.holeRound2")}
              </label>
              <input
                data-testid="input-createFormHoleType-holeRound2"
                name="hole_round_2"
                type="number"
                value={set.hole_round_2}
                onChange={(e) => handleChange(index, "hole_round_2", e.target.value)}
                placeholder="0"
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Hole Slotted */}
            <div>
              <label
                data-testid="text-createFormHoleType-holeSlotted"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.holeSlotted")}
              </label>
              <input
                data-testid="input-createFormHoleType-holeSlotted"
                name="hole_slotted"
                type="number"
                value={set.hole_slotted}
                onChange={(e) => handleChange(index, "hole_slotted", e.target.value)}
                placeholder="0"
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Price Slotted */}
            <div>
              <label
                data-testid="text-createFormHoleType-priceSlotted"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.priceSlotted")}
              </label>
              <input
                data-testid="input-createFormHoleType-priceSlotted"
                name="price_slotted"
                type="number"
                value={set.price_slotted}
                onChange={(e) => handleChange(index, "price_slotted", e.target.value)}
                placeholder="0"
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Pattern */}
            <div>
              <label
                data-testid="text-createFormHoleType-pattern"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.pattern")}
              </label>
              <input
                data-testid="input-createFormHoleType-pattern"
                name="pattern"
                type="text"
                value={set.pattern}
                onChange={(e) => handleChange(index, "pattern", e.target.value)}
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      ))}

      {/* Add button */}
      <div className="flex justify-end mb-8">
        <button
          data-testid="button-createFormHoleType-addForm"
          name="buttonAddFormHoleType"
          type="button"
          onClick={handleAddSet}
          className="w-12 h-12 rounded-full bg-green-500 hover:bg-green-600 flex items-center justify-center text-white shadow-md transition-colors duration-200"
        >
          <FaPlus size={24} />
        </button>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 my-5">
        <SaveButton
          dataTestId="button-createFormHoleType-saveHoleType"
          onClick={handleSave}
        />
        <CancelButton
          dataTestId="button-createFormHoleType-cancelHoleType"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default CreateHoleType;
